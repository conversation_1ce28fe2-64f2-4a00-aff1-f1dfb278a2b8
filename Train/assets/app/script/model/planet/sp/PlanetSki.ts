import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

// 入口图1下层
export default class PlanetSki extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-1502, -500)


    public async die() {
        this.dead = false
        this.map.nextNode()
        return false
    }

}


// 入口图2上层
export class PlanetSki2 extends PlanetEmptyNode {
    public reachOffset: cc.Vec2 = cc.v2(-700, 200)
    public originOffset: cc.Vec2 = cc.v2(200, 200)
    public jumpOffset: cc.Vec2 = cc.v2(400, 250)

    private isSyncDie: boolean = false

    public async die() {
        let succ = await this.syncDie()
        this.dead = false
        this.map.nextNode()
        return succ
    }

    public async syncDie() {
        if (this.isSyncDie) return true
        this.isSyncDie = true
        let map = this.map
        let data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassRageModeMessage, {
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index - 1
        })
        data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassRageModeMessage, {
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index
        })

        if (data.code != 0) {
            viewHelper.showNetError(data.code)
            return false
        }

        let from = this.index
        let to = Math.min(this.map.getNodeCount(), this.index + 6)
        gameHelper.hero.enterSkiMode(from, to, this.map)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)
        return true
    }

}