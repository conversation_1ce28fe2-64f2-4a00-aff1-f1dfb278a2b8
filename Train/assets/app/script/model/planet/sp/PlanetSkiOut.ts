import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

// 出口图1下层
export default class PlanetSkiOut1 extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-502, -500)


    public async die() {
        this.dead = false
        this.map.nextNode()
        return false
    }

}


// 出口图2上层
export class PlanetSkiOut2 extends PlanetEmptyNode {
    public reachOffset: cc.Vec2 = cc.v2(-200, 200)

    private isSyncDie: boolean = false

    public async die() {
        let succ = await this.syncDie()
        this.dead = false
        this.map.nextNode()
        return succ
    }

}