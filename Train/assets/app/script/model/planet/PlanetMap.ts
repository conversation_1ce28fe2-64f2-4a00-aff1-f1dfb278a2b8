import { PlanetEvent, PlanetNodeType } from "../../common/constant/Enums"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import EventType from "../../common/event/EventType"
import PlanetCheckPointModel from "./PlanetCheckPointModel"
import PlanetEmptyNode from "./PlanetEmptyNode"
import PlanetMineModel from "./PlanetMineModel"
import PlanetModel from "./PlanetModel"
import PlanetNodeModel from "./PlanetNodeModel"
import PlanetPuzzleModel from "./PlanetPuzzleModel"
import PlanetArea from "./PlanetArea"
import PlanetBranch from "./PlanetBranch"
import PlanetQuestionModel from "./PlanetQuestionModel"
import PlanetRandomBox from "./sp/PlanetRandomBox"
import PlanetTimeLimitBox from "./sp/PlanetTimeLimitBox"
import PlanetMonsterBox from "./sp/PlanetMonsterBox"
import PlanetTool<PERSON>less from "./sp/PlanetToolBless"
import PlanetRageMode from "./sp/PlanetRageMode"
import PlanetSki, { PlanetSki2 } from "./sp/PlanetSki"
import PlanetSkiOut1, { PlanetSkiOut2 } from "./sp/PlanetSkiOut"

/**
 * 星球地图
 */

const CLASS_MAP = {
    [PlanetEvent.RANDOM_BOX]: PlanetRandomBox,
    [PlanetEvent.TIME_LIMIT_BOX]: PlanetTimeLimitBox,
    [PlanetEvent.MONSTER_BOX]: PlanetMonsterBox,
    [PlanetEvent.TOOL_BLESS]: PlanetToolBless,
    [PlanetEvent.RAGE_MODE]: PlanetRageMode,
    [PlanetEvent.LIBRARY_GUESS]: PlanetPuzzleModel,
    [PlanetEvent.SKI_R1]: PlanetSki,
    [PlanetEvent.SKI_R2]: PlanetSki2,
    [PlanetEvent.SKI_O1]: PlanetSkiOut1,
    [PlanetEvent.SKI_O2]: PlanetSkiOut2,
}

export default class PlanetMap {
    protected id: number = null

    protected json: any = null

    protected width: number = 0

    protected progress: number = 0

    protected nodes: (PlanetMineModel | PlanetCheckPointModel | PlanetNodeModel | PlanetEmptyNode)[] = []

    protected planetId: number = null
    protected startPos: cc.Vec2 = null
    public showLandAnim: boolean = false

    public planet: PlanetModel = null

    protected _area: PlanetArea = null

    public init(planet: PlanetModel, id: number) {
        this.id = id
        let planetId = planet.getId()
        this.planetId = planetId
        this.planet = planet
        this.initJson()
        let json = this.json
        this.width = json.width
        if (json.start) {
            this.startPos = cc.v2(json.start.x, json.start.y)
        }
        this.initNodes()
        return this
    }

    protected initJson() {
        this.json = cfgHelper.getPlanetJsonData(this.planetId, "PlanetMap", this.id)
    }

    public toDB() {
        return {
            id: this.id,
            showLandAnim: this.showLandAnim,
        }
    }

    public fromDB({ id, showLandAnim }, planet: PlanetModel) {
        this.showLandAnim = showLandAnim
        return this.init(planet, id)
    }

    public initNodes() {
        this.nodes = []
        let nodes = this.json.node || []
        for (let i = 0; i < nodes.length; i++) {
            let data = nodes[i]
            let { type, x, y, eventName, angle, scale, mineId, reachOffset, id, index } = data
            let planetId = this.planetId
            let nodeId = `${planetId}-${this.getId()}-${id}`
            let model: PlanetNodeModel = this.newModel(data.type, data)
            if (model) {
                model.setMap(this)
                model.nodeType = type
                model.init(nodeId, mineId)
                if (scale) {
                    model.scale = scale
                }
                if (reachOffset) {
                    model.reachOffset = reachOffset
                }
                model.typeIndex = id
                model.setPosition(cc.v2(x, y))
                model.setIndex(index)
                model.eventName = eventName
                model.orgPosition = model.position.clone()
                model.angle = angle || 0
                model.orgAngle = angle
                this.nodes.push(model)
            }
        }
    }

    protected newModel(type: PlanetNodeType, data?: any) {
        if (type == PlanetNodeType.MINE) {
            return new PlanetMineModel()
        } else if (type == PlanetNodeType.CHECK_POINT) {
            return new PlanetCheckPointModel()
        } else if (type == PlanetNodeType.NONE) {
            let eventName = data?.eventName
            const Cls = CLASS_MAP[eventName]
            if (Cls) {
                return new Cls()
            }
            return new PlanetEmptyNode()
        } else if (type == PlanetNodeType.QUESTION) {
            return new PlanetQuestionModel()
        }
    }

    public initNodeEnd() {
        for (let node of this.nodes) {
            node.isEnd = node.isPass()
        }
    }

    get name() {
        let name = this.json?.name
        let area = this.getArea()
        if (area.getMaps()[0] == this && !name) {
            name = area.name
        }
        return name
    }

    get darkColor() { return this.json.darkColor }

    public getId() { return this.id }
    public getPlanetId() { return this.planetId }
    public getCheckPoints(): PlanetCheckPointModel[] { return this.nodes.filter(node => node instanceof PlanetCheckPointModel) as any }
    public getNodes() { return this.nodes }
    public getNodeCount() { return this.nodes.length }
    public getWidth() { return this.width }
    public getProgress() { return this.progress }
    public getPlanet() { return this.planet }
    public getStartPos() { return this.startPos }
    public getUnCompleteNodes() { return this.nodes.slice(this.progress) }
    public getArea() {
        if (!this._area) {
            this._area = this.planet.getAreas().find(a => a.getMaps().has(this))
        }
        return this._area
    }

    public getCurNode() {
        let nodes = this.getNodes()
        return nodes[this.progress]
    }

    public getPreNode() {
        let nodes = this.getNodes()
        return nodes[this.progress - 1]
    }

    public getNextNode() {
        let nodes = this.getNodes()
        return nodes[this.progress + 1]
    }

    public nextNode(count: number = 1) {
        this.setProgress(this.progress + count)
    }

    public isDone() {
        return !this.getCurNode()
    }

    public setProgress(progress: number) {
        this.progress = progress
        this.planet.curNode = this.getCurNode()
        let preNode = this.getPreNode()
        if (this.planet.isDone()) {
            gameHelper.planet.unlockByLockId(this.planetId)
        }
        else if (preNode && preNode.nodeType == PlanetNodeType.CHECK_POINT) {
            gameHelper.planet.unlockByLockId(preNode.getId())
        }
        eventCenter.emit(EventType.PLANET_NODE_COMPLETE, this.planetId, preNode)
        if (this.planet.isDone()) {
            eventCenter.emit(EventType.PLANET_COMPLETE, this.planetId)
        }
    }

    public setDone() {
        this.setProgress(this.getNodeCount())
    }

    public needLandAnim() {
        if (this.showLandAnim) return
        if (this.progress > 0) return
        return this.name
    }

    public getBranch(): PlanetBranch {
        return null
    }

    update(dt) {
        if (this.planet.getBranchCurMap() != this) return
        let curNode = this.getCurNode()
        curNode?.update(dt)
    }

    public getNodeByIndex(index: number) {
        return this.nodes[index - 1]
    }

    public getPreEmptyNode(eventName: string) {
        for (let i = this.progress - 1; i >= 0; i--) {
            let node = this.nodes[i]
            if (node instanceof PlanetEmptyNode && (eventName && node.eventName == eventName)) {
                return node
            }
        }
    }
}