import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetSki from "../../../../model/planet/sp/PlanetSki";
import PlanetSik from "../../../../model/planet/sp/PlanetSki";
import PlanetSkiOut1 from "../../../../model/planet/sp/PlanetSkiOut";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SkiOut1Cmpt extends PlanetNodeCmpt {

    public model: PlanetSkiOut1 = null
    private actionTree: ActionTree = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSki) {
        if (this.model != model) return

        let hero = gameHelper.hero

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await this.model.die()
        this.model.end()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}