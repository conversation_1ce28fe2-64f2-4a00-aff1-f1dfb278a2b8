import { HeroAction, HeroAnimation } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import { PlanetSki2 } from "../../../../model/planet/sp/PlanetSki";
import HeroCmpt from "../../hero/HeroCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

enum State {
    NONE,
    REACH,
    CLICK,
}

@ccclass
export default class SkiCmpt2 extends PlanetNodeCmpt {

    public model: PlanetSki2 = null
    private actionTree: ActionTree = null
    private heroNode: cc.Node = null
    private mapNode: cc.Node = null
    private state: State = State.NONE

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.heroNode = planetCtrl.heroNode_
        this.mapNode = planetCtrl.mapNode_
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSki2) {
        if (this.model != model) return

        let hero = gameHelper.hero

        if (this.model.progress) {
            this.state = State.CLICK
            this.Child("body").active = false
            this.waitToSkiMode()
        }
        else {
            // 移动到洞口
            await this.actionTree.start(async (action: ActionNode) => {
                await action.run(hero.moveToPos, model.reachPosition, hero)
                hero.setAction(HeroAction.IDLE)
                action.ok()
            })
            this.state = State.REACH
        }
    }

    public async onClick() {
        if (this.state != State.REACH) return
        this.state = State.CLICK
        this.Child("body").active = false
        this.model.progress = 1

        let hero = gameHelper.hero
        let model = this.model

        // 透明
        cc.Tween.stopAllByTarget(this.heroNode)
        await cc.tween(this.heroNode).to(.2, { opacity: 0 }).start().promise()

        // 移动到原点
        model.reachOffset = model.originOffset
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 层级调整
        const heroIndex = this.mapNode.children.indexOf(this.heroNode)
        const skiIndex = this.mapNode.children.indexOf(this.node)
        this.mapNode.children.splice(heroIndex, 1)
        this.mapNode.children.splice(skiIndex, 0, this.heroNode)

        // 恢复透明度
        this.heroNode.opacity = 255

        // 播放跳跃动画
        await this.heroNode.Component(HeroCmpt).playAnimation(HeroAnimation.SKI_JUMP)

        this.waitToSkiMode()
    }

    public async waitToSkiMode() {
        let hero = gameHelper.hero
        let model = this.model
        while (true) {
            await this.actionTree.start(hero.waitSkiMode, hero)
            let succ = await model.syncDie()
            if (succ) {
                model.end()
                break
            }
            if (!cc.isValid(this)) return
        }
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}