import { HeroAction, HeroAnimation } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import { PlanetSki2 } from "../../../../model/planet/sp/PlanetSki";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SkiCmpt2 extends PlanetNodeCmpt {

    public model: PlanetSki2 = null
    private actionTree: ActionTree = null
    private heroNode: cc.Node = null
    private mapNode: cc.Node = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.heroNode = planetCtrl.heroNode_
        this.mapNode = planetCtrl.mapNode_
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model: PlanetSki2) {
        if (this.model != model) return
        let hero = gameHelper.hero

        // 移动到洞口
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 透明
        cc.Tween.stopAllByTarget(this.heroNode)
        await cc.tween(this.heroNode).to(.2, { opacity: 0 }).start().promise()

        // 移动到原点 
        model.reachOffset = model.originOffset
        await this.actionTree.start(async (action: ActionNode) => {
            await action.run(hero.moveToPos, model.reachPosition, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        // 层级  TODO:不知道为什么 setSiblingIndex 没作用
        const heroIndex = this.mapNode.children.indexOf(this.heroNode)
        const skiIndex = this.mapNode.children.indexOf(this.node)
        this.mapNode.children.splice(heroIndex, 1)
        this.mapNode.children.splice(skiIndex, 0, this.heroNode)

        // 透明
        this.heroNode.opacity = 255
        // 快速移动配合跳出去

        // model.reachOffset = model.jumpOffset
        // await this.actionTree.start(async (action: ActionNode) => {
        //     hero.setAction(HeroAction.SKI_JUMP)
        //     hero.setPathByPos(model.reachPosition)
        //     await action.run(hero.onMove)
        //     hero.setAction(HeroAction.SKI_ING)
        //     action.ok()
        // })
        let succ = await model.die()
        if (succ) {
            model.end()
        }
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}